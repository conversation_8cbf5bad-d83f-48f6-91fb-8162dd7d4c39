Ideas for regression optimization:
Compound Regression Scenario: “Multiplayer Survival + Blueprints + Radiation Test”

An integrated test scenario designed to cover blueprint placement, time-critical multiplayer workflows, and survival mechanics all in a single guided playthrough

Features include:

    Two players in a multiplayer lobby with invite, reconnect, and visibility settings
    Spawn on Europa with active radiation weather
    Paste large airtight blueprint with DLC blocks, functional rooms
    Test energy/oxygen usage and radiation shielding (grid + atmospheric + artificial)
    Simulate ship mining and resource collection
    Validate DLC-specific blocks, cargo access terminals, projector tools, and multiplayer synchronization

This case significantly reduces test hours by combining dozens of legacy cases into one immersive scenario.
Frostbite Scenario — Accelerated Regression via “Save Splitting”

A two-phase testing plan to dramatically streamline long regression runs for the Frostbite storyline:

Phase A: Setup a save.

    Progress through data container retrieval and facility exploration
    Gather all four data containers plus ID cards
    Return to base and plant basic defenses
    Create a save labeled PhaseA_Complete

Phase B: Actual test

    Load PhaseA_Complete and jump right into the base defense wave
    Engage drones and verify scenario completion and achievement triggers
    This method allows repeated regression of only the final combat/final trigger, saving ~45 minutes per tester per run.

Learning to Survive <PERSON><PERSON><PERSON> — Two‑Phase Skip Strategy

A similar phased approach for the in-depth Learning to Survive tutorial/mission:

Phase A: Full Run save creation.

    Complete all educational tasks across asteroid bases (mining, pressurization, production, jump drive, combat, etc.)
    Reach the final mission panel and save the game (as PhaseA_LearningComplete)

Phase B: Final Mission Execution test.

    Load the phase‑A save and directly perform the final combat or engineering task required to win the scenario
    Validate mission completion dialogue and achievement unlocks
    This plan avoids replaying multiple tutorial stages and ensures consistent coverage of the tutorial’s end-to-end logic.

Add test cases for:

Fieldwork Update
Lab blocks (Freezer, Vat, Desks, Tanks)
Pipes, Cargo Access Terminal
Keyboard binding modifiers (Ctrl+Z, Ctrl+Y, F5, etc.)
Admin & Projector UI improvements, inertia/piston impulse settings
Contact Pack
PvE encounters, faction claiming NPC grids
New endgame Prototech & Factorum blocks
Toolbar Actions, scenario menu overhaul, multiple connectivity improvements
Signal Pack
Broadcast Controller, Action Relay, Compact Antenna
QoL: scenario selection UI, grid claiming, emotes
Automatons Pack
Grid AI (programmable block, timers, sensors, event controller)
Robot cockpits, pipes, and access panels
Survival & Food
Food, Foraging, Farming
Environmental hazards (Radiation, Weather)