,Section ,Sub-Section,QA,"Change Needed?
(Yes/No)",Suggestion/Comment
BASICS - ASTRONAUT,Astronaut Actions,Astronaut Actions,Ondrej B.,No,
,,Welding,Ondrej B.,No,
,,Grinding,Ondrej B.,No,
,,Drilling,Ondrej B.,No,
,,Shooting Keyboard,Ondrej B.,Yes,"Can add shooting when moving (walk, run, sprint, fly with jet-pack), to the ""..in correct direction"" TC, or as new separate one; Can also add something like ""Check bullet/impact marks on blocks after shooting them""; Also can add ""Aim assist is NOT working with KB+M."""
,,Shooting Gamepad Aim Assist,Ondrej B.,No,
,,Emotes,Ondrej B.,No,
,Astronaut Customization ,Astronaut Customization ,Ondrej B.,No,
,,Unknown Signals and Skins,Ondrej B.,Yes,Strong signals (competitive) have now new colors - green when unclaimed and light green when claimed. See SE-27758 to better see the colors. Note: Has to be enabled in world settings
,Astronaut Movement,Astronaut Movement,Ondrej B.,No,
,,Jet-Pack Mode,Ondrej B.,No,
,,Jet-Pack Mode - Dampeners,Ondrej B.,No,
,,<PERSON><PERSON><PERSON><PERSON>,Ondrej B.,No,
,,<PERSON><PERSON><PERSON><PERSON>,<PERSON>drej B.,No,
,Astronaut Statistics,Astronaut Statistics,Ondrej B.,Yes,"TC mentions only Cryo Chamber, but now there is also Inset Cryo Room and Lab Vat with same functions."
,,Respawn,Ondrej B.,No,
,,Health,Ondrej B.,Yes,"Consider adding TC(s) about possibility to die due to radiation (I suppose possible, do not know much about it)"
,,Oxygen,Ondrej B.,No,
,,Energy,Ondrej B.,No,
,,Hydrogen,Ondrej B.,No,
,Player Persistency,Player Persistency,Ondrej B.,No,
,Survival Gameplay,Survival Gameplay,Neeraj S.,No,
,,NPC/AI,Neeraj S.,No,
BASICS - GRID,Blueprints,Blueprints,Neeraj S.,No,
,,Projections,Neeraj S.,Yes,We can add an additional check for the 'Mark Missing Blocks' and 'Mark Unfinished Blocks' options.
,Building and PCU,Building and PCU,Neeraj S.,No,
,,Recolor,Neeraj S.,No,
,,Block Rotation,Neeraj S.,Yes,I think we need to update the 'Rotation of grid on axes...' related test cases in this section. Please refer to ticket SE-29424.
,,Block Boundaries,Neeraj S.,No,
,,Symmetry / Mirror Mode,Neeraj S.,No,
,,Building Modes - Single Block,Neeraj S.,No,
,,Building Modes - Line,Neeraj S.,Yes,"We can add an additional test case to check if the Building Mode setting persists in the selected mode after the game restarts or, crashes."
,,Building Modes - Plane,Neeraj S.,Yes,"We can add an additional test case to check if the Building Mode setting persists in the selected mode after the game restarts or, crashes."
,,PCU,Neeraj S.,No,
,Grid Actions,Tools & Weapons - Welding,Neeraj S.,No,
,,Tools & Weapons - Grinding,Neeraj S.,No,
,,Tools & Weapons - Drilling ,Neeraj S.,No,
,,Tools & Weapons - Shooting,Neeraj S.,No,
,,Tools & Weapons - Turrets,Neeraj S.,No,
,,Tools & Weapons - Inventory,Neeraj S.,No,
,,Landing,Neeraj S.,No,
,,Jumping,Neeraj S.,Yes,We can add a test case to check if the jump effects and sounds differ between the vanilla and Protech jump drives.
,,Merging,Neeraj S.,No,
,,Connecting,Neeraj S.,No,
,Grid Movement,Grid Movement,Neeraj S.,No,
,,Characters on / inside grid,Neeraj S.,No,
,,Dampeners,Neeraj S.,Yes,"We can add a test case to check if the correct thrusters are in action, when damping is active and thruster effects are visible."
,Land Vehicles,Land Vehicles,Neeraj S.,No,
,,Characters on / inside grid,Neeraj S.,No,
,,Brakes,Neeraj S.,No,
BASICS - SCREENS,Admin Screen,Admin Screen,Neeraj S.,No,
,,Admin Tools,Neeraj S.,Yes,"TCs are missing for the options, 'Keep Original Ownership on Paste', 'Ignore Safe Zone', and 'Ignore PCU Limits'."
,,Cycle Objects,Neeraj S.,Yes,"TC missing for the option, 'Simulate all Locally'. "
,,Trash Removal,Neeraj S.,Yes,Do we need test cases for checking the default values under Trash Removal > Other as well?
,,Trash Removal - Voxel,Neeraj S.,No,
,,Entity List,Neeraj S.,Yes,Do we need test cases for checking the 'Remove inactive owner and his grid' option? 
,,Safe Zones,Neeraj S.,Yes,Do we need test cases for checking the 'Configure Filter' button? 
,,Global Permissions,Neeraj S.,No,
,Factions and Communications,Factions,Neeraj S.,No,
,,GPS,Neeraj S.,No,
,,Antennas,Neeraj S.,No,
,,Chat,Neeraj S.,No,
,,Voice Chat,Neeraj S.,No,
,Spawn Menu,Items,Neeraj S.,Yes,"Since items can be found by typing as well, we should add a test case for that."
,,Procedural Asteroids,Neeraj S.,No,
,,Planets,Neeraj S.,Yes,Test Case (T320673) for 'Testing planets...' can now be removed.
,,Predefined Asteroids,Neeraj S.,No,
GENERAL,Installation and First run,Installation and First run,Neeraj S.,No,
,Crash Reports,Crash Reports,Neeraj S.,No,
,Grid Destructions,Grid Destructions,,,
,In-Game Workshop,In-Game Workshop,Ondrej B.,No,
,,Worlds,Ondrej B.,No,
,,Blueprints,Ondrej B.,No,
,,Scripts,Ondrej B.,No,
,,Mods,Ondrej B.,Yes,Can not rate from within the game. This TC can be most likely deleted.
,Mods,Mods,Neeraj S.,Yes, 'Supporting document with screenshots' no longer accessible (Access Denied).
,,Updated Mods - Text HUD API,Neeraj S.,No,
,,Updated Mods - Modular Encounters Systems,Neeraj S.,No,
,,Updated Mods - Camera Panning,Neeraj S.,No,
,,Updated Mods - Rich Hug Master + Build Vision 3.0,Neeraj S.,No,
,,Updated Mods - Buildinfo,Neeraj S.,No,
,,Updated Mods - Defense Shields,Neeraj S.,Yes,Steam link not working anymore.
,,Updated Mods - WeaponCore,Neeraj S.,Yes,Steam link not working anymore.
,,Updated Mods - Colorful Icons,Neeraj S.,No,
,,Updated Mods - Nanobot Build and Repair Systems,Neeraj S.,No,
,,Updated Mods - Bot_spawner + AI Enabled,Neeraj S.,No,
,ModSDK,ModSDK,Ondrej B.,No,
,Render,Render,Ondrej B.,No,
,Saves,Backwards Compatibility,Ondrej B.,No,
,,Corrupted Saves,Ondrej B.,No,
NEW GAME - CLIENT,Quick Start,Quick Start,Neeraj S.,No,
,Sandbox,Sandbox,Neeraj S.,No,
,,Actions,Neeraj S.,Yes,"We can add a test case to check if the player can join a friend’s game/lobby via Steam Client > Friends > View Friends List > Click the drop-down on the friend’s name/profile icon (who is playing SE), and then select 'Join Game'."
,,Experimental vs Safe connections ,Neeraj S.,No,
,Story Scenarios,Story Scenarios,Neeraj S.,No,
,,Actions,Neeraj S.,Yes,"We can add a test case to check if the player can join a friend’s game/lobby via Steam Client > Friends > View Friends List > Click the drop-down on the friend’s name/profile icon (who is playing SE), and then select 'Join Game'."
,,Experimental vs Safe connections ,Neeraj S.,No,
,PvP Scenarios,PvP Scenarios,Neeraj S.,No,
,,Actions,Neeraj S.,Yes,"We can add a test case to check if the player can join a friend’s game/lobby via Steam Client > Friends > View Friends List > Click the drop-down on the friend’s name/profile icon (who is playing SE), and then select 'Join Game'."
,,Experimental vs Safe connections ,Neeraj S.,No,
,Original Content,Original Content,Neeraj S.,No,
,,Actions,Neeraj S.,Yes,"We can add a test case to check if the player can join a friend’s game/lobby via Steam Client > Friends > View Friends List > Click the drop-down on the friend’s name/profile icon (who is playing SE), and then select 'Join Game'."
,,Experimental vs Safe connections ,Neeraj S.,No,
,Scenario Completion,First Jump,Neeraj S.,No,
,,Frostbite,Neeraj S.,No,
,,Learning to Survive,Neeraj S.,Yes,SE-29994
,,Never Surrender,Neeraj S.,Yes,SE-29996
,,Space Standoff,Neeraj S.,No,
,,Sparks of the Future,Neeraj S.,No,
,,Lost Colony,Neeraj S.,No,
,,Scrap Race,Neeraj S.,No,
,,Uranium Heist,Neeraj S.,No,
NEW GAME - DS,Actions,Actions,Neeraj S.,No,
,Experimental vs Safe connections ,Experimental vs Safe connections ,Neeraj S.,No,